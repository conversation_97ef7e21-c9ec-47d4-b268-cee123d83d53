import * as problem from './problem';

describe('Algorithm Patterns: Prefix Sum', () => {
  describe('prefixSum', () => {
    test('should return empty array for empty input', () => {
      expect(prefixSum([])).toEqual([]);
    });

    test('should handle single element', () => {
      expect(prefixSum([5])).toEqual([5]);
    });

    test('should calculate prefix sum for positive numbers', () => {
      expect(prefixSum([1, 2, 3, 4])).toEqual([1, 3, 6, 10]);
    });

    test('should handle negative numbers', () => {
      expect(prefixSum([-1, 2, -3, 4])).toEqual([-1, 1, -2, 2]);
    });

    test('should handle zeros', () => {
      expect(prefixSum([0, 1, 0, 2])).toEqual([0, 1, 1, 3]);
    });

    test('should handle large numbers', () => {
      expect(prefixSum([100, 200, 300])).toEqual([100, 300, 600]);
    });
  });
});